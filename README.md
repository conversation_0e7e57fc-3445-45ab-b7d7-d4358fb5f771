# Simple SSE Agent

A real-time chat application demonstrating Server-Sent Events (SSE) with an AI agent backend.

## Tech Stack

**Frontend:**
- Next.js 14 with TypeScript
- React 18
- Server-Sent Events for real-time streaming

**Backend:**
- FastAPI (Python)
- LangGraph for AI agent workflows
- <PERSON><PERSON><PERSON><PERSON> with OpenAI integration
- Uvicorn ASGI server

## Quick Start

### Prerequisites
- Node.js 18+
- Python 3.8+
- OpenAI API key

### Setup

1. **Install frontend dependencies:**
   ```bash
   npm install
   ```

2. **Install backend dependencies:**
   ```bash
   cd backend
   pip install -r requirements.txt
   ```

3. **Set up environment variables:**
   ```bash
   # Add your OpenAI API key
   export OPENAI_API_KEY="your-api-key-here"
   ```

### Running the Application

1. **Start the backend server:**
   ```bash
   cd backend
   uvicorn app:app --reload --port 8000
   ```

2. **Start the frontend (in a new terminal):**
   ```bash
   npm run dev
   ```

3. **Open your browser:**
   Navigate to `http://localhost:3000`

## Features

- Real-time streaming chat interface
- AI agent powered by LangGraph and LangChain
- Server-Sent Events for live response streaming
- TypeScript for type safety
- CORS-enabled API for cross-origin requests

## Project Structure

```
├── app/                 # Next.js app directory
├── backend/            # FastAPI backend
│   ├── app.py         # Main FastAPI application
│   ├── graph.py       # LangGraph agent definition
│   └── requirements.txt
├── components/         # React components
└── package.json       # Frontend dependencies
```

## API Endpoints

- `GET /agent?query=<your-question>` - Stream AI agent responses via SSE
