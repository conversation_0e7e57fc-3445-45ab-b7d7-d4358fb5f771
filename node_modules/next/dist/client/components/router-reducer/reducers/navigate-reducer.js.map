{"version": 3, "sources": ["../../../../../src/client/components/router-reducer/reducers/navigate-reducer.ts"], "names": ["handleExternalUrl", "navigateReducer", "state", "mutable", "url", "pendingPush", "mpaNavigation", "canonicalUrl", "scrollableSegments", "undefined", "handleMutable", "generateSegmentsFromPatch", "flightRouterPatch", "segments", "segment", "parallelRoutes", "Object", "keys", "length", "parallelRouteKey", "parallelRoute", "entries", "childSegment", "push", "triggerLazyFetchForLeafSegments", "newCache", "currentCache", "flightSegmentPath", "treePatch", "appliedPatch", "rsc", "prefetchRsc", "loading", "Map", "segmentPathsToFill", "map", "segmentPaths", "clearCacheNodeDataForSegmentPath", "process", "env", "__NEXT_PPR", "navigateReducer_PPR", "navigateReducer_noPPR", "action", "isExternalUrl", "navigateType", "shouldScroll", "hash", "href", "createHrefFromUrl", "prune<PERSON><PERSON><PERSON>tch<PERSON><PERSON>", "prefetchCache", "preserveCustomHistoryState", "toString", "prefetchValues", "getOrCreatePrefetchCacheEntry", "nextUrl", "tree", "buildId", "treeAtTimeOfPrefetch", "data", "prefetchQueue", "bump", "then", "flightData", "canonicalUrlOverride", "isFirstRead", "lastUsedTime", "Date", "now", "document", "getElementById", "currentTree", "cache", "flightDataPath", "slice", "flightSegmentPathWithLeadingEmpty", "newTree", "applyRouterStatePatchToTree", "isNavigatingToNewRootLayout", "createEmptyCacheNode", "applied", "status", "PrefetchCacheEntryStatus", "stale", "applyFlightData", "hardNavigate", "shouldHardNavigate", "invalidateCacheBelowFlightSegmentPath", "subSegment", "scrollableSegmentPath", "DEFAULT_SEGMENT_KEY", "patchedTree", "hashFragment", "_postponed", "prefetchedTree", "seedData", "head", "task", "updateCacheNodeOnNavigation", "node", "patchedRouterState", "route", "listenForDynamicRequest", "fetchServerResponse"], "mappings": ";;;;;;;;;;;;;;;IAiCgBA,iBAAiB;eAAjBA;;IAqEHC,eAAe;eAAfA;;;qCAjGuB;mCACF;uDACoB;6CACV;oCACT;6CACS;oCAOrC;+BACuB;iCACE;iCACF;2BACO;yBACD;gCAI7B;oCAIA;kDAC0C;AAE1C,SAASD,kBACdE,KAA2B,EAC3BC,OAAgB,EAChBC,GAAW,EACXC,WAAoB;IAEpBF,QAAQG,aAAa,GAAG;IACxBH,QAAQI,YAAY,GAAGH;IACvBD,QAAQE,WAAW,GAAGA;IACtBF,QAAQK,kBAAkB,GAAGC;IAE7B,OAAOC,IAAAA,4BAAa,EAACR,OAAOC;AAC9B;AAEA,SAASQ,0BACPC,iBAAoC;IAEpC,MAAMC,WAAgC,EAAE;IACxC,MAAM,CAACC,SAASC,eAAe,GAAGH;IAElC,IAAII,OAAOC,IAAI,CAACF,gBAAgBG,MAAM,KAAK,GAAG;QAC5C,OAAO;YAAC;gBAACJ;aAAQ;SAAC;IACpB;IAEA,KAAK,MAAM,CAACK,kBAAkBC,cAAc,IAAIJ,OAAOK,OAAO,CAC5DN,gBACC;QACD,KAAK,MAAMO,gBAAgBX,0BAA0BS,eAAgB;YACnE,mEAAmE;YACnE,IAAIN,YAAY,IAAI;gBAClBD,SAASU,IAAI,CAAC;oBAACJ;uBAAqBG;iBAAa;YACnD,OAAO;gBACLT,SAASU,IAAI,CAAC;oBAACT;oBAASK;uBAAqBG;iBAAa;YAC5D;QACF;IACF;IAEA,OAAOT;AACT;AAEA,SAASW,gCACPC,QAAmB,EACnBC,YAAuB,EACvBC,iBAAoC,EACpCC,SAA4B;IAE5B,IAAIC,eAAe;IAEnBJ,SAASK,GAAG,GAAGJ,aAAaI,GAAG;IAC/BL,SAASM,WAAW,GAAGL,aAAaK,WAAW;IAC/CN,SAASO,OAAO,GAAGN,aAAaM,OAAO;IACvCP,SAASV,cAAc,GAAG,IAAIkB,IAAIP,aAAaX,cAAc;IAE7D,MAAMmB,qBAAqBvB,0BAA0BiB,WAAWO,GAAG,CACjE,CAACrB,UAAY;eAAIa;eAAsBb;SAAQ;IAGjD,KAAK,MAAMsB,gBAAgBF,mBAAoB;QAC7CG,IAAAA,kEAAgC,EAACZ,UAAUC,cAAcU;QAEzDP,eAAe;IACjB;IAEA,OAAOA;AACT;AAKO,MAAM5B,kBAAkBqC,QAAQC,GAAG,CAACC,UAAU,GACjDC,sBACAC;AAEJ,8EAA8E;AAC9E,4EAA4E;AAC5E,SAASA,sBACPxC,KAA2B,EAC3ByC,MAAsB;IAEtB,MAAM,EAAEvC,GAAG,EAAEwC,aAAa,EAAEC,YAAY,EAAEC,YAAY,EAAE,GAAGH;IAC3D,MAAMxC,UAAmB,CAAC;IAC1B,MAAM,EAAE4C,IAAI,EAAE,GAAG3C;IACjB,MAAM4C,OAAOC,IAAAA,oCAAiB,EAAC7C;IAC/B,MAAMC,cAAcwC,iBAAiB;IACrC,wFAAwF;IACxFK,IAAAA,sCAAkB,EAAChD,MAAMiD,aAAa;IAEtChD,QAAQiD,0BAA0B,GAAG;IAErC,IAAIR,eAAe;QACjB,OAAO5C,kBAAkBE,OAAOC,SAASC,IAAIiD,QAAQ,IAAIhD;IAC3D;IAEA,MAAMiD,iBAAiBC,IAAAA,iDAA6B,EAAC;QACnDnD;QACAoD,SAAStD,MAAMsD,OAAO;QACtBC,MAAMvD,MAAMuD,IAAI;QAChBC,SAASxD,MAAMwD,OAAO;QACtBP,eAAejD,MAAMiD,aAAa;IACpC;IACA,MAAM,EAAEQ,oBAAoB,EAAEC,IAAI,EAAE,GAAGN;IAEvCO,8BAAa,CAACC,IAAI,CAACF;IAEnB,OAAOA,KAAKG,IAAI,CACd;YAAC,CAACC,YAAYC,qBAAqB;QACjC,IAAIC,cAAc;QAClB,iCAAiC;QACjC,IAAI,CAACZ,eAAea,YAAY,EAAE;YAChC,gGAAgG;YAChGb,eAAea,YAAY,GAAGC,KAAKC,GAAG;YACtCH,cAAc;QAChB;QAEA,4DAA4D;QAC5D,IAAI,OAAOF,eAAe,UAAU;YAClC,OAAOhE,kBAAkBE,OAAOC,SAAS6D,YAAY3D;QACvD;QAEA,mEAAmE;QACnE,wCAAwC;QACxC,IAAIiE,SAASC,cAAc,CAAC,yBAAyB;YACnD,OAAOvE,kBAAkBE,OAAOC,SAAS6C,MAAM3C;QACjD;QAEA,IAAImE,cAActE,MAAMuD,IAAI;QAC5B,MAAM/B,eAAexB,MAAMuE,KAAK;QAChC,IAAIjE,qBAA0C,EAAE;QAChD,KAAK,MAAMkE,kBAAkBV,WAAY;YACvC,MAAMrC,oBAAoB+C,eAAeC,KAAK,CAC5C,GACA,CAAC;YAEH,0DAA0D;YAC1D,MAAM/C,YAAY8C,eAAeC,KAAK,CAAC,CAAC,EAAE,CAAC,EAAE;YAE7C,sBAAsB;YACtB,MAAMC,oCAAoC;gBAAC;mBAAOjD;aAAkB;YAEpE,wEAAwE;YACxE,IAAIkD,UAAUC,IAAAA,wDAA2B,EACvC,sBAAsB;YACtBF,mCACAJ,aACA5C,WACAoB;YAGF,kGAAkG;YAClG,6IAA6I;YAC7I,IAAI6B,YAAY,MAAM;gBACpBA,UAAUC,IAAAA,wDAA2B,EACnC,sBAAsB;gBACtBF,mCACAjB,sBACA/B,WACAoB;YAEJ;YAEA,IAAI6B,YAAY,MAAM;gBACpB,IAAIE,IAAAA,wDAA2B,EAACP,aAAaK,UAAU;oBACrD,OAAO7E,kBAAkBE,OAAOC,SAAS6C,MAAM3C;gBACjD;gBAEA,MAAMoE,QAAmBO,IAAAA,+BAAoB;gBAC7C,IAAIC,UAAU;gBAEd,IACE3B,eAAe4B,MAAM,KAAKC,4CAAwB,CAACC,KAAK,IACxD,CAAClB,aACD;oBACA,yJAAyJ;oBACzJ,uHAAuH;oBACvH,gFAAgF;oBAChF,0FAA0F;oBAC1Fe,UAAUzD,gCACRiD,OACA/C,cACAC,mBACAC;oBAEF,yEAAyE;oBACzE,mFAAmF;oBACnF0B,eAAea,YAAY,GAAGC,KAAKC,GAAG;gBACxC,OAAO;oBACLY,UAAUI,IAAAA,gCAAe,EACvB3D,cACA+C,OACAC,gBACApB;gBAEJ;gBAEA,MAAMgC,eAAeC,IAAAA,sCAAkB,EACrC,sBAAsB;gBACtBX,mCACAJ;gBAGF,IAAIc,cAAc;oBAChB,2CAA2C;oBAC3Cb,MAAM3C,GAAG,GAAGJ,aAAaI,GAAG;oBAC5B2C,MAAM1C,WAAW,GAAGL,aAAaK,WAAW;oBAE5CyD,IAAAA,4EAAqC,EACnCf,OACA/C,cACAC;oBAEF,8EAA8E;oBAC9ExB,QAAQsE,KAAK,GAAGA;gBAClB,OAAO,IAAIQ,SAAS;oBAClB9E,QAAQsE,KAAK,GAAGA;gBAClB;gBAEAD,cAAcK;gBAEd,KAAK,MAAMY,cAAc9E,0BAA0BiB,WAAY;oBAC7D,MAAM8D,wBAAwB;2BAAI/D;2BAAsB8D;qBAAW;oBACnE,kFAAkF;oBAClF,IACEC,qBAAqB,CAACA,sBAAsBxE,MAAM,GAAG,EAAE,KACvDyE,4BAAmB,EACnB;wBACAnF,mBAAmBe,IAAI,CAACmE;oBAC1B;gBACF;YACF;QACF;QAEAvF,QAAQyF,WAAW,GAAGpB;QACtBrE,QAAQI,YAAY,GAAG0D,uBACnBhB,IAAAA,oCAAiB,EAACgB,wBAClBjB;QACJ7C,QAAQE,WAAW,GAAGA;QACtBF,QAAQK,kBAAkB,GAAGA;QAC7BL,QAAQ0F,YAAY,GAAG9C;QACvB5C,QAAQ2C,YAAY,GAAGA;QAEvB,OAAOpC,IAAAA,4BAAa,EAACR,OAAOC;IAC9B,GACA,IAAMD;AAEV;AAEA,8EAA8E;AAC9E,8EAA8E;AAC9E,0BAA0B;AAC1B,SAASuC,oBACPvC,KAA2B,EAC3ByC,MAAsB;IAEtB,MAAM,EAAEvC,GAAG,EAAEwC,aAAa,EAAEC,YAAY,EAAEC,YAAY,EAAE,GAAGH;IAC3D,MAAMxC,UAAmB,CAAC;IAC1B,MAAM,EAAE4C,IAAI,EAAE,GAAG3C;IACjB,MAAM4C,OAAOC,IAAAA,oCAAiB,EAAC7C;IAC/B,MAAMC,cAAcwC,iBAAiB;IACrC,wFAAwF;IACxFK,IAAAA,sCAAkB,EAAChD,MAAMiD,aAAa;IAEtChD,QAAQiD,0BAA0B,GAAG;IAErC,IAAIR,eAAe;QACjB,OAAO5C,kBAAkBE,OAAOC,SAASC,IAAIiD,QAAQ,IAAIhD;IAC3D;IAEA,MAAMiD,iBAAiBC,IAAAA,iDAA6B,EAAC;QACnDnD;QACAoD,SAAStD,MAAMsD,OAAO;QACtBC,MAAMvD,MAAMuD,IAAI;QAChBC,SAASxD,MAAMwD,OAAO;QACtBP,eAAejD,MAAMiD,aAAa;IACpC;IACA,MAAM,EAAEQ,oBAAoB,EAAEC,IAAI,EAAE,GAAGN;IAEvCO,8BAAa,CAACC,IAAI,CAACF;IAEnB,OAAOA,KAAKG,IAAI,CACd;YAAC,CAACC,YAAYC,sBAAsB6B,WAAW;QAC7C,IAAI5B,cAAc;QAClB,iCAAiC;QACjC,IAAI,CAACZ,eAAea,YAAY,EAAE;YAChC,gGAAgG;YAChGb,eAAea,YAAY,GAAGC,KAAKC,GAAG;YACtCH,cAAc;QAChB;QAEA,4DAA4D;QAC5D,IAAI,OAAOF,eAAe,UAAU;YAClC,OAAOhE,kBAAkBE,OAAOC,SAAS6D,YAAY3D;QACvD;QAEA,mEAAmE;QACnE,wCAAwC;QACxC,IAAIiE,SAASC,cAAc,CAAC,yBAAyB;YACnD,OAAOvE,kBAAkBE,OAAOC,SAAS6C,MAAM3C;QACjD;QAEA,IAAImE,cAActE,MAAMuD,IAAI;QAC5B,MAAM/B,eAAexB,MAAMuE,KAAK;QAChC,IAAIjE,qBAA0C,EAAE;QAChD,qEAAqE;QACrE,qEAAqE;QACrE,6DAA6D;QAC7D,gBAAgB;QAChB,KAAK,MAAMkE,kBAAkBV,WAAY;YACvC,MAAMrC,oBAAoB+C,eAAeC,KAAK,CAC5C,GACA,CAAC;YAEH,0DAA0D;YAC1D,MAAM/C,YAAY8C,eAAeC,KAAK,CAAC,CAAC,EAAE,CAAC,EAAE;YAE7C,sBAAsB;YACtB,MAAMC,oCAAoC;gBAAC;mBAAOjD;aAAkB;YAEpE,wEAAwE;YACxE,IAAIkD,UAAUC,IAAAA,wDAA2B,EACvC,sBAAsB;YACtBF,mCACAJ,aACA5C,WACAoB;YAGF,kGAAkG;YAClG,6IAA6I;YAC7I,IAAI6B,YAAY,MAAM;gBACpBA,UAAUC,IAAAA,wDAA2B,EACnC,sBAAsB;gBACtBF,mCACAjB,sBACA/B,WACAoB;YAEJ;YAEA,IAAI6B,YAAY,MAAM;gBACpB,IAAIE,IAAAA,wDAA2B,EAACP,aAAaK,UAAU;oBACrD,OAAO7E,kBAAkBE,OAAOC,SAAS6C,MAAM3C;gBACjD;gBAEA,IACE,iEAAiE;gBACjE,+DAA+D;gBAC/D,+DAA+D;gBAC/D,0BAA0B;gBAC1B,oEAAoE;gBACpE,iEAAiE;gBACjE,uBAAuB;gBACvBqE,eAAexD,MAAM,KAAK,GAC1B;oBACA,MAAM6E,iBAAoCrB,cAAc,CAAC,EAAE;oBAC3D,MAAMsB,WAAWtB,cAAc,CAAC,EAAE;oBAClC,MAAMuB,OAAOvB,cAAc,CAAC,EAAE;oBAE9B,MAAMwB,OAAOC,IAAAA,2CAA2B,EACtCzE,cACA8C,aACAuB,gBACAC,UACAC;oBAEF,IAAIC,SAAS,QAAQA,KAAKE,IAAI,KAAK,MAAM;wBACvC,iEAAiE;wBACjE,4DAA4D;wBAE5D,+DAA+D;wBAC/D,sDAAsD;wBACtD,qDAAqD;wBACrD,8BAA8B;wBAC9B,MAAMC,qBAAwCH,KAAKI,KAAK;wBACxDzB,UAAUwB;wBAEV,MAAM5E,WAAWyE,KAAKE,IAAI;wBAE1B,6DAA6D;wBAC7D,mCAAmC;wBACnC,EAAE;wBACF,iEAAiE;wBACjE,+DAA+D;wBAC/D,yDAAyD;wBACzD,2DAA2D;wBAC3D,6DAA6D;wBAC7D,+DAA+D;wBAC/D,kEAAkE;wBAClE,kEAAkE;wBAClE,iEAAiE;wBACjE,gDAAgD;wBAChDG,IAAAA,uCAAuB,EACrBL,MACAM,IAAAA,wCAAmB,EACjBpG,KACAoE,aACAtE,MAAMsD,OAAO,EACbtD,MAAMwD,OAAO;wBAIjBvD,QAAQsE,KAAK,GAAGhD;oBAClB,OAAO;wBACL,2CAA2C;wBAC3C,kEAAkE;wBAClE,8DAA8D;wBAC9D,mBAAmB;wBACnBoD,UAAUkB;oBACZ;gBACF,OAAO;oBACL,6DAA6D;oBAC7D,0CAA0C;oBAC1C,6DAA6D;oBAC7D,+DAA+D;oBAC/D,mEAAmE;oBACnE,yDAAyD;oBACzD,qBAAqB;oBACrB,MAAMtB,QAAmBO,IAAAA,+BAAoB;oBAC7C,IAAIC,UAAU;oBAEd,IACE3B,eAAe4B,MAAM,KAAKC,4CAAwB,CAACC,KAAK,IACxD,CAAClB,aACD;wBACA,yJAAyJ;wBACzJ,uHAAuH;wBACvH,gFAAgF;wBAChF,0FAA0F;wBAC1Fe,UAAUzD,gCACRiD,OACA/C,cACAC,mBACAC;wBAEF,yEAAyE;wBACzE,mFAAmF;wBACnF0B,eAAea,YAAY,GAAGC,KAAKC,GAAG;oBACxC,OAAO;wBACLY,UAAUI,IAAAA,gCAAe,EACvB3D,cACA+C,OACAC,gBACApB;oBAEJ;oBAEA,MAAMgC,eAAeC,IAAAA,sCAAkB,EACrC,sBAAsB;oBACtBX,mCACAJ;oBAGF,IAAIc,cAAc;wBAChB,2CAA2C;wBAC3Cb,MAAM3C,GAAG,GAAGJ,aAAaI,GAAG;wBAC5B2C,MAAM1C,WAAW,GAAGL,aAAaK,WAAW;wBAE5CyD,IAAAA,4EAAqC,EACnCf,OACA/C,cACAC;wBAEF,8EAA8E;wBAC9ExB,QAAQsE,KAAK,GAAGA;oBAClB,OAAO,IAAIQ,SAAS;wBAClB9E,QAAQsE,KAAK,GAAGA;oBAClB;gBACF;gBAEAD,cAAcK;gBAEd,KAAK,MAAMY,cAAc9E,0BAA0BiB,WAAY;oBAC7D,MAAM8D,wBAAwB;2BAAI/D;2BAAsB8D;qBAAW;oBACnE,kFAAkF;oBAClF,IACEC,qBAAqB,CAACA,sBAAsBxE,MAAM,GAAG,EAAE,KACvDyE,4BAAmB,EACnB;wBACAnF,mBAAmBe,IAAI,CAACmE;oBAC1B;gBACF;YACF;QACF;QAEAvF,QAAQyF,WAAW,GAAGpB;QACtBrE,QAAQI,YAAY,GAAG0D,uBACnBhB,IAAAA,oCAAiB,EAACgB,wBAClBjB;QACJ7C,QAAQE,WAAW,GAAGA;QACtBF,QAAQK,kBAAkB,GAAGA;QAC7BL,QAAQ0F,YAAY,GAAG9C;QACvB5C,QAAQ2C,YAAY,GAAGA;QAEvB,OAAOpC,IAAAA,4BAAa,EAACR,OAAOC;IAC9B,GACA,IAAMD;AAEV"}