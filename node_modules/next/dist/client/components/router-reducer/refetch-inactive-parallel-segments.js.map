{"version": 3, "sources": ["../../../../src/client/components/router-reducer/refetch-inactive-parallel-segments.ts"], "names": ["addRefreshMarkerToActiveParallelSegments", "refreshInactiveParallelSegments", "options", "fetchedSegments", "Set", "refreshInactiveParallelSegmentsImpl", "rootTree", "updatedTree", "state", "updatedCache", "includeNextUrl", "parallelRoutes", "refetch<PERSON>ath", "refetch<PERSON><PERSON><PERSON>", "fetchPromises", "location", "pathname", "search", "has", "add", "fetchPromise", "fetchServerResponse", "URL", "origin", "nextUrl", "buildId", "then", "fetchResponse", "flightData", "flightDataPath", "applyFlightData", "push", "key", "parallelFetchPromise", "Promise", "all", "tree", "path", "segment", "includes", "PAGE_SEGMENT_KEY"], "mappings": ";;;;;;;;;;;;;;;IA8GgBA,wCAAwC;eAAxCA;;IArFMC,+BAA+B;eAA/BA;;;iCAtBU;qCACI;yBACH;AAoB1B,eAAeA,gCACpBC,OAAwC;IAExC,MAAMC,kBAAkB,IAAIC;IAC5B,MAAMC,oCAAoC;QACxC,GAAGH,OAAO;QACVI,UAAUJ,QAAQK,WAAW;QAC7BJ;IACF;AACF;AAEA,eAAeE,oCAAoC,KAUlD;IAVkD,IAAA,EACjDG,KAAK,EACLD,WAAW,EACXE,YAAY,EACZC,cAAc,EACdP,eAAe,EACfG,WAAWC,WAAW,EAIvB,GAVkD;IAWjD,MAAM,GAAGI,gBAAgBC,aAAaC,cAAc,GAAGN;IACvD,MAAMO,gBAAgB,EAAE;IAExB,IACEF,eACAA,gBAAgBG,SAASC,QAAQ,GAAGD,SAASE,MAAM,IACnDJ,kBAAkB,aAClB,4FAA4F;IAC5F,sDAAsD;IACtD,CAACV,gBAAgBe,GAAG,CAACN,cACrB;QACAT,gBAAgBgB,GAAG,CAACP,aAAa,2BAA2B;;QAE5D,wHAAwH;QACxH,kIAAkI;QAClI,MAAMQ,eAAeC,IAAAA,wCAAmB,EACtC,IAAIC,IAAIV,aAAaG,SAASQ,MAAM,GACpC,gGAAgG;QAChG,8HAA8H;QAC9H;YAACjB,QAAQ,CAAC,EAAE;YAAEA,QAAQ,CAAC,EAAE;YAAEA,QAAQ,CAAC,EAAE;YAAE;SAAU,EAClDI,iBAAiBF,MAAMgB,OAAO,GAAG,MACjChB,MAAMiB,OAAO,EACbC,IAAI,CAAC,CAACC;YACN,MAAMC,aAAaD,aAAa,CAAC,EAAE;YACnC,IAAI,OAAOC,eAAe,UAAU;gBAClC,KAAK,MAAMC,kBAAkBD,WAAY;oBACvC,wFAAwF;oBACxF,4GAA4G;oBAC5G,4EAA4E;oBAC5EE,IAAAA,gCAAe,EAACrB,cAAcA,cAAcoB;gBAC9C;YACF,OAAO;YACL,4GAA4G;YAC5G,+GAA+G;YAC/G,sEAAsE;YACxE;QACF;QAEAf,cAAciB,IAAI,CAACX;IACrB;IAEA,IAAK,MAAMY,OAAOrB,eAAgB;QAChC,MAAMsB,uBAAuB5B,oCAAoC;YAC/DG;YACAD,aAAaI,cAAc,CAACqB,IAAI;YAChCvB;YACAC;YACAP;YACAG;QACF;QAEAQ,cAAciB,IAAI,CAACE;IACrB;IAEA,MAAMC,QAAQC,GAAG,CAACrB;AACpB;AAQO,SAASd,yCACdoC,IAAuB,EACvBC,IAAY;IAEZ,MAAM,CAACC,SAAS3B,kBAAkBE,cAAc,GAAGuB;IACnD,oGAAoG;IACpG,IAAIE,QAAQC,QAAQ,CAACC,yBAAgB,KAAK3B,kBAAkB,WAAW;QACrEuB,IAAI,CAAC,EAAE,GAAGC;QACVD,IAAI,CAAC,EAAE,GAAG;IACZ;IAEA,IAAK,MAAMJ,OAAOrB,eAAgB;QAChCX,yCAAyCW,cAAc,CAACqB,IAAI,EAAEK;IAChE;AACF"}