{"version": 3, "sources": ["../../../../src/build/babel/loader/get-config.ts"], "names": ["getConfig", "nextDistPath", "fileExtensionRegex", "getCacheCharacteristics", "loaderOptions", "source", "filename", "isServer", "pagesDir", "isPageFile", "startsWith", "isNextDist", "test", "hasModuleExports", "indexOf", "fileExt", "exec", "getPlugins", "cacheCharacteristics", "hasReactRefresh", "development", "applyCommonJsItem", "createConfigItem", "require", "type", "reactRefreshItem", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pageConfigItem", "disallowExportAllItem", "transformDefineItem", "resolve", "nextSsgItem", "commonJsItem", "nextFontUnsupported", "filter", "Boolean", "isJsonFile", "isJsFile", "getCustomBabelConfig", "config<PERSON><PERSON><PERSON><PERSON>", "babelConfigRaw", "readFileSync", "JSON5", "parse", "Error", "babelConfigWarned", "checkCustomBabelConfigDeprecation", "config", "Object", "keys", "length", "plugins", "presets", "otherOptions", "isPresetReadyToDeprecate", "pluginReasons", "unsupportedPlugins", "Array", "isArray", "plugin", "pluginName", "push", "Log", "warn", "join", "getFreshConfig", "target", "inputSourceMap", "hasJsxRuntime", "configFile", "cwd", "customConfig", "undefined", "options", "babelrc", "cloneInputAst", "sourceMaps", "sourceMap", "sourceFileName", "env", "overrides", "caller", "name", "supportsStaticESM", "supportsDynamicImport", "supportsTopLevelAwait", "isDev", "defineProperty", "enumerable", "writable", "value", "reason", "emitWarning", "loadedOptions", "loadOptions", "consumeIterator", "loadConfig", "get<PERSON><PERSON><PERSON><PERSON>", "flags", "config<PERSON><PERSON>", "Map", "configFiles", "Set", "addDependency", "cache<PERSON>ey", "has", "cachedConfig", "get", "root", "add", "info", "freshConfig", "call", "set"], "mappings": ";;;;+BAiYA;;;eAAwBA;;;oBAjYK;8DACX;sBAE4B;sEACvB;sBAGS;6DACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAErB,MAAMC,eACJ;AA6BF,MAAMC,qBAAqB;AAC3B,SAASC,wBACPC,aAAqC,EACrCC,MAAc,EACdC,QAAgB;QAMAJ;IAJhB,MAAM,EAAEK,QAAQ,EAAEC,QAAQ,EAAE,GAAGJ;IAC/B,MAAMK,aAAaH,SAASI,UAAU,CAACF;IACvC,MAAMG,aAAaV,aAAaW,IAAI,CAACN;IACrC,MAAMO,mBAAmBR,OAAOS,OAAO,CAAC,sBAAsB,CAAC;IAC/D,MAAMC,UAAUb,EAAAA,2BAAAA,mBAAmBc,IAAI,CAACV,8BAAxBJ,wBAAmC,CAAC,EAAE,KAAI;IAE1D,OAAO;QACLK;QACAE;QACAE;QACAE;QACAE;IACF;AACF;AAEA;;;CAGC,GACD,SAASE,WACPb,aAAqC,EACrCc,oBAAqD;IAErD,MAAM,EAAEX,QAAQ,EAAEE,UAAU,EAAEE,UAAU,EAAEE,gBAAgB,EAAE,GAC1DK;IAEF,MAAM,EAAEC,eAAe,EAAEC,WAAW,EAAE,GAAGhB;IAEzC,MAAMiB,oBAAoBR,mBACtBS,IAAAA,sBAAgB,EAACC,QAAQ,wBAAwB;QAAEC,MAAM;IAAS,KAClE;IACJ,MAAMC,mBAAmBN,kBACrBG,IAAAA,sBAAgB,EACd;QACEC,QAAQ;QACR;YAAEG,cAAc;QAAK;KACtB,EACD;QAAEF,MAAM;IAAS,KAEnB;IACJ,MAAMG,iBACJ,CAACpB,YAAYE,aACTa,IAAAA,sBAAgB,EAAC;QAACC,QAAQ;KAA+B,EAAE;QACzDC,MAAM;IACR,KACA;IACN,MAAMI,wBACJ,CAACrB,YAAYE,aACTa,IAAAA,sBAAgB,EACd;QAACC,QAAQ;KAAuD,EAChE;QAAEC,MAAM;IAAS,KAEnB;IACN,MAAMK,sBAAsBP,IAAAA,sBAAgB,EAC1C;QACEC,QAAQO,OAAO,CAAC;QAChB;YACE,wBAAwBV,cAAc,gBAAgB;YACtD,iBAAiBb,WAAW,cAAc;YAC1C,mBAAmBA,WAAW,QAAQ;QACxC;QACA;KACD,EACD;QAAEiB,MAAM;IAAS;IAEnB,MAAMO,cACJ,CAACxB,YAAYE,aACTa,IAAAA,sBAAgB,EAAC;QAACC,QAAQO,OAAO,CAAC;KAAiC,EAAE;QACnEN,MAAM;IACR,KACA;IACN,MAAMQ,eAAerB,aACjBW,IAAAA,sBAAgB,EACdC,QAAQ,+DACR;QAAEC,MAAM;IAAS,KAEnB;IACJ,MAAMS,sBAAsBX,IAAAA,sBAAgB,EAC1C;QAACC,QAAQ;KAAoC,EAC7C;QAAEC,MAAM;IAAS;IAGnB,OAAO;QACLC;QACAE;QACAC;QACAP;QACAQ;QACAE;QACAC;QACAC;KACD,CAACC,MAAM,CAACC;AACX;AAEA,MAAMC,aAAa;AACnB,MAAMC,WAAW;AAEjB;;;;;CAKC,GACD,SAASC,qBAAqBC,cAAsB;IAClD,IAAIH,WAAWpB,IAAI,CAACuB,iBAAiB;QACnC,MAAMC,iBAAiBC,IAAAA,gBAAY,EAACF,gBAAgB;QACpD,OAAOG,cAAK,CAACC,KAAK,CAACH;IACrB,OAAO,IAAIH,SAASrB,IAAI,CAACuB,iBAAiB;QACxC,OAAOhB,QAAQgB;IACjB;IACA,MAAM,IAAIK,MACR;AAEJ;AAEA,IAAIC,oBAAoB;AACxB;;;;;CAKC,GACD,SAASC,kCACPC,MAAuC;IAEvC,IAAI,CAACA,UAAUC,OAAOC,IAAI,CAACF,QAAQG,MAAM,KAAK,GAAG;QAC/C;IACF;IAEA,MAAM,EAAEC,OAAO,EAAEC,OAAO,EAAE,GAAGC,cAAc,GAAGN;IAC9C,IAAIC,OAAOC,IAAI,CAACI,gBAAgB,CAAC,GAAGH,MAAM,GAAG,GAAG;QAC9C;IACF;IAEA,IAAIL,mBAAmB;QACrB;IACF;IAEAA,oBAAoB;IAEpB,MAAMS,2BACJ,CAACF,WACDA,QAAQF,MAAM,KAAK,KAClBE,QAAQF,MAAM,KAAK,KAAKE,OAAO,CAAC,EAAE,KAAK;IAC1C,MAAMG,gBAAgB,EAAE;IACxB,MAAMC,qBAAqB,EAAE;IAE7B,IAAIC,MAAMC,OAAO,CAACP,UAAU;QAC1B,KAAK,MAAMQ,UAAUR,QAAS;YAC5B,MAAMS,aAAaH,MAAMC,OAAO,CAACC,UAAUA,MAAM,CAAC,EAAE,GAAGA;YAEvD,wFAAwF;YACxF,6CAA6C;YAC7C,OAAQC;gBACN,KAAK;gBACL,KAAK;oBACHL,cAAcM,IAAI,CAChB,CAAC,0FAA0F,CAAC;oBAE9F;gBACF,KAAK;oBACHN,cAAcM,IAAI,CAChB,CAAC,qFAAqF,CAAC;oBAEzF;gBACF,KAAK;oBACHN,cAAcM,IAAI,CAChB,CAAC,gFAAgF,CAAC;oBAEpF;gBACF,KAAK;oBACHN,cAAcM,IAAI,CAChB,CAAC,qGAAqG,CAAC;oBAEzG;gBACF,KAAK;oBACHN,cAAcM,IAAI,CAChB,CAAC,8FAA8F,CAAC;oBAElG;gBACF;oBACEL,mBAAmBK,IAAI,CAACD;oBACxB;YACJ;QACF;IACF;IAEA,IAAIN,4BAA4BE,mBAAmBN,MAAM,KAAK,GAAG;QAC/DY,KAAIC,IAAI,CACN,CAAC,uEAAuE,EACtER,cAAcL,MAAM,GAAG,IAAI,MAAM,IAClC,CAAC;QAGJ,IAAIK,cAAcL,MAAM,GAAG,GAAG;YAC5BY,KAAIC,IAAI,CAAC,CAAC,kDAAkD,CAAC;YAC7DD,KAAIC,IAAI,CAACR,cAAcS,IAAI,CAAC;YAC5BF,KAAIC,IAAI,CACN,CAAC,4HAA4H,CAAC;QAElI;IACF;AACF;AAEA;;;CAGC,GACD,SAASE,eAEP/C,oBAAqD,EACrDd,aAAqC,EACrC8D,MAAc,EACd5D,QAAgB,EAChB6D,cAA8B;IAE9B,IAAI,EAAE5D,QAAQ,EAAEC,QAAQ,EAAEY,WAAW,EAAEgD,aAAa,EAAEC,UAAU,EAAEC,GAAG,EAAE,GACrElE;IAEF,IAAImE,eAAoBF,aACpB/B,qBAAqB+B,cACrBG;IAEJ1B,kCAAkCyB;IAElC,IAAIE,UAAU;QACZC,SAAS;QACTC,eAAe;QACfrE;QACA6D,gBAAgBA,kBAAkBK;QAElC,sEAAsE;QACtE,4CAA4C;QAC5CI,YACExE,cAAcwE,UAAU,KAAKJ,YACzB,IAAI,CAACK,SAAS,GACdzE,cAAcwE,UAAU;QAE9B,qEAAqE;QACrE,qEAAqE;QACrE,WAAW;QACXE,gBAAgBxE;QAEhB6C,SAAS;eACJlC,WAAWb,eAAec;eACzBqD,CAAAA,gCAAAA,aAAcpB,OAAO,KAAI,EAAE;SAChC;QAED,oCAAoC;QACpCe,QAAQ3D,WAAWiE,YAAYD,gCAAAA,aAAcL,MAAM;QACnD,iCAAiC;QACjCa,GAAG,EAAER,gCAAAA,aAAcQ,GAAG;QAEtB3B,SAAS,AAAC,CAAA;YACR,uEAAuE;YACvE,IAAImB,gCAAAA,aAAcnB,OAAO,EAAE;gBACzB,OAAOmB,aAAanB,OAAO;YAC7B;YAEA,6EAA6E;YAC7E,IAAImB,cAAc;gBAChB,OAAOC;YACT;YAEA,mEAAmE;YACnE,OAAO;gBAAC;aAAa;QACvB,CAAA;QAEAQ,WAAW5E,cAAc4E,SAAS;QAElCC,QAAQ;YACNC,MAAM;YACNC,mBAAmB;YACnBC,uBAAuB;YAEvB,oDAAoD;YACpD,mDAAmD;YACnDlB,QAAQA;YAER,gEAAgE;YAChE,sEAAsE;YACtE,sBAAsB;YACtBmB,uBAAuB;YAEvB9E;YACA+D;YACA9D;YACA8E,OAAOlE;YACPgD;YAEA,GAAGhE,cAAc6E,MAAM;QACzB;IACF;IAEA,qEAAqE;IACrE,IAAI,OAAOR,QAAQP,MAAM,KAAK,aAAa;QACzC,OAAOO,QAAQP,MAAM;IACvB;IAEAlB,OAAOuC,cAAc,CAACd,QAAQQ,MAAM,EAAE,aAAa;QACjDO,YAAY;QACZC,UAAU;QACVC,OAAO,CAACC;YACN,IAAI,CAAEA,CAAAA,kBAAkB/C,KAAI,GAAI;gBAC9B+C,SAAS,IAAI/C,MAAM+C;YACrB;YACA,IAAI,CAACC,WAAW,CAACD;QACnB;IACF;IAEA,MAAME,gBAAgBC,IAAAA,iBAAW,EAACrB;IAClC,MAAM1B,SAASgD,IAAAA,qBAAe,EAACC,IAAAA,sBAAU,EAACH;IAE1C,OAAO9C;AACT;AAEA;;;;CAIC,GACD,SAASkD,YAAY/E,oBAAqD;IACxE,MAAM,EAAEX,QAAQ,EAAEE,UAAU,EAAEE,UAAU,EAAEE,gBAAgB,EAAEE,OAAO,EAAE,GACnEG;IAEF,MAAMgF,QACJ,IACC3F,CAAAA,WAAW,IAAS,CAAA,IACpBE,CAAAA,aAAa,IAAS,CAAA,IACtBE,CAAAA,aAAa,IAAS,CAAA,IACtBE,CAAAA,mBAAmB,IAAS,CAAA;IAE/B,OAAOE,UAAUmF;AACnB;AAGA,MAAMC,cAAqC,IAAIC;AAC/C,MAAMC,cAA2B,IAAIC;AAEtB,SAAStG,UAEtB,EACEK,MAAM,EACN6D,MAAM,EACN9D,aAAa,EACbE,QAAQ,EACR6D,cAAc,EAOf;IAED,MAAMjD,uBAAuBf,wBAC3BC,eACAC,QACAC;IAGF,IAAIF,cAAciE,UAAU,EAAE;QAC5B,qFAAqF;QACrF,IAAI,CAACkC,aAAa,CAACnG,cAAciE,UAAU;IAC7C;IAEA,MAAMmC,WAAWP,YAAY/E;IAC7B,IAAIiF,YAAYM,GAAG,CAACD,WAAW;QAC7B,MAAME,eAAeP,YAAYQ,GAAG,CAACH;QAErC,OAAO;YACL,GAAGE,YAAY;YACfjC,SAAS;gBACP,GAAGiC,aAAajC,OAAO;gBACvBH,KAAKlE,cAAckE,GAAG;gBACtBsC,MAAMxG,cAAckE,GAAG;gBACvBhE;gBACAwE,gBAAgBxE;YAClB;QACF;IACF;IAEA,IAAIF,cAAciE,UAAU,IAAI,CAACgC,YAAYI,GAAG,CAACrG,cAAciE,UAAU,GAAG;QAC1EgC,YAAYQ,GAAG,CAACzG,cAAciE,UAAU;QACxCP,KAAIgD,IAAI,CACN,CAAC,wCAAwC,EAAE1G,cAAciE,UAAU,CAAC,CAAC;IAEzE;IAEA,MAAM0C,cAAc9C,eAAe+C,IAAI,CACrC,IAAI,EACJ9F,sBACAd,eACA8D,QACA5D,UACA6D;IAGFgC,YAAYc,GAAG,CAACT,UAAUO;IAE1B,OAAOA;AACT"}