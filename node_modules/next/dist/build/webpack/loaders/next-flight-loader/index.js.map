{"version": 3, "sources": ["../../../../../src/build/webpack/loaders/next-flight-loader/index.ts"], "names": ["transformSource", "getAssumedSourceType", "noopHeadPath", "require", "resolve", "MODULE_PROXY_PATH", "mod", "sourceType", "buildInfo", "getModuleBuildInfo", "detectedClientEntryType", "rsc", "clientEntryType", "clientRefs", "assumedSourceType", "length", "includes", "source", "sourceMap", "Error", "_module", "getRSCModuleInformation", "resourceKey", "resourcePath", "matchResource", "startsWith", "BARREL_OPTIMIZATION_PREFIX", "formatBarrelOptimizedResource", "type", "RSC_MODULE_TYPES", "client", "parser", "callback", "esmSource", "cnt", "ref", "warnOnce", "replacedSource", "replace", "RSC_MOD_REF_PROXY_ALIAS"], "mappings": ";;;;;;;;;;;;;;;IA8CA,OAkGC;eAlGuBA;;IA7BRC,oBAAoB;eAApBA;;;2BAhBwB;4BAIjC;0BACkB;mCACe;uBACM;oCACX;AAEnC,MAAMC,eAAeC,QAAQC,OAAO,CAAC;AACrC,gEAAgE;AAChE,MAAMC,oBACJ;AAGK,SAASJ,qBACdK,GAAmB,EACnBC,UAAsB;QAGUC,gBACbA;IAFnB,MAAMA,YAAYC,IAAAA,sCAAkB,EAACH;IACrC,MAAMI,0BAA0BF,8BAAAA,iBAAAA,UAAWG,GAAG,qBAAdH,eAAgBI,eAAe;IAC/D,MAAMC,aAAaL,CAAAA,8BAAAA,kBAAAA,UAAWG,GAAG,qBAAdH,gBAAgBK,UAAU,KAAI,EAAE;IAEnD,4EAA4E;IAC5E,6EAA6E;IAC7E,4DAA4D;IAC5D,IAAIC,oBAAoBP;IACxB,IAAIO,sBAAsB,UAAUJ,4BAA4B,QAAQ;QACtE,IACEG,WAAWE,MAAM,KAAK,KACrBF,WAAWE,MAAM,KAAK,KAAKF,UAAU,CAAC,EAAE,KAAK,IAC9C;YACA,uEAAuE;YACvE,yEAAyE;YACzE,oBAAoB;YACpBC,oBAAoB;QACtB,OAAO,IAAI,CAACD,WAAWG,QAAQ,CAAC,MAAM;YACpC,2CAA2C;YAC3CF,oBAAoB;QACtB;IACF;IACA,OAAOA;AACT;AAEe,SAASd,gBAEtBiB,MAAc,EACdC,SAAc;QAyBV,6BAAA,eAQAV,gBAiDAA;IAhFJ,8BAA8B;IAC9B,IAAI,OAAOS,WAAW,UAAU;QAC9B,MAAM,IAAIE,MAAM;IAClB;IAEA,gDAAgD;IAChD,mEAAmE;IACnE,MAAMX,YAAYC,IAAAA,sCAAkB,EAAC,IAAI,CAACW,OAAO;IACjDZ,UAAUG,GAAG,GAAGU,IAAAA,0CAAuB,EAACJ,QAAQ;IAEhD,2EAA2E;IAC3E,gFAAgF;IAChF,UAAU;IACV,EAAE;IACF,6EAA6E;IAC7E,iFAAiF;IACjF,gFAAgF;IAChF,iFAAiF;IACjF,uBAAuB;IACvB,EAAE;IACF,0EAA0E;IAC1E,sBAAsB;IACtB,IAAIK,cAAsB,IAAI,CAACC,YAAY;IAC3C,KAAI,gBAAA,IAAI,CAACH,OAAO,sBAAZ,8BAAA,cAAcI,aAAa,qBAA3B,4BAA6BC,UAAU,CAACC,sCAA0B,GAAG;QACvEJ,cAAcK,IAAAA,oCAA6B,EACzCL,aACA,IAAI,CAACF,OAAO,CAACI,aAAa;IAE9B;IAEA,qBAAqB;IACrB,IAAIhB,EAAAA,iBAAAA,UAAUG,GAAG,qBAAbH,eAAeoB,IAAI,MAAKC,4BAAgB,CAACC,MAAM,EAAE;YAGjD,sBAAA;QAFF,MAAMhB,oBAAoBb,qBACxB,IAAI,CAACmB,OAAO,GACZ,iBAAA,IAAI,CAACA,OAAO,sBAAZ,uBAAA,eAAcW,MAAM,qBAApB,qBAAsBxB,UAAU;QAElC,MAAMM,aAAaL,UAAUG,GAAG,CAACE,UAAU;QAE3C,IAAIC,sBAAsB,UAAU;YAClC,IAAID,WAAWG,QAAQ,CAAC,MAAM;gBAC5B,IAAI,CAACgB,QAAQ,CACX,IAAIb,MACF,CAAC,oGAAoG,CAAC;gBAG1G;YACF;YAEA,IAAIc,YAAY,CAAC;6BACM,EAAE5B,kBAAkB;sCACX,EAAEiB,YAAY;;;;;;;;AAQpD,CAAC;YACK,IAAIY,MAAM;YACV,KAAK,MAAMC,OAAOtB,WAAY;gBAC5B,IAAIsB,QAAQ,IAAI;oBACdF,aAAa,CAAC,wCAAwC,EAAEX,YAAY,KAAK,CAAC;gBAC5E,OAAO,IAAIa,QAAQ,WAAW;oBAC5BF,aAAa,CAAC;;uCAEe,EAAEX,YAAY;AACrD,CAAC;gBACO,OAAO;oBACLW,aAAa,CAAC;OACjB,EAAEC,IAAI,2BAA2B,EAAEZ,YAAY,CAAC,EAAEa,IAAI;UACnD,EAAED,MAAM,IAAI,EAAEC,IAAI,GAAG,CAAC;gBACxB;YACF;YAEA,IAAI,CAACH,QAAQ,CAAC,MAAMC,WAAWf;YAC/B;QACF;IACF;IAEA,IAAIV,EAAAA,kBAAAA,UAAUG,GAAG,qBAAbH,gBAAeoB,IAAI,MAAKC,4BAAgB,CAACC,MAAM,EAAE;QACnD,IAAI5B,iBAAiB,IAAI,CAACqB,YAAY,EAAE;YACtCa,IAAAA,kBAAQ,EACN,CAAC,0OAA0O,CAAC;QAEhP;IACF;IAEA,MAAMC,iBAAiBpB,OAAOqB,OAAO,CACnCC,kCAAuB,EACvBlC;IAEF,IAAI,CAAC2B,QAAQ,CAAC,MAAMK,gBAAgBnB;AACtC"}