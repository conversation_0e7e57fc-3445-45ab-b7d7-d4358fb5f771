{"version": 3, "sources": ["../../src/server/config-utils.ts"], "names": ["loadWebpackHook", "installed", "init", "initWebpack", "require", "addHookAliases", "map", "request", "replacement", "resolve"], "mappings": ";;;;+BAEgBA;;;eAAAA;;;AAFhB,IAAIC,YAAqB;AAElB,SAASD;IACd,MAAM,EAAEE,MAAMC,WAAW,EAAE,GAAGC,QAAQ;IACtC,IAAIH,WAAW;QACb;IACF;IACAA,YAAY;IACZE;IAEA,wDAAwD;IACxD,4DAA4D;IAC5DC,QAAQ,0BAA0BC,cAAc,CAC9C;QACE;YAAC;YAAW;SAAyC;QACrD;YAAC;YAAmB;SAAqC;QACzD;YAAC;YAAwB;SAAqC;QAC9D;YAAC;YAAuB;SAAyC;QACjE;YAAC;YAA0B;SAAyC;QACpE;YACE;YACA;SACD;QACD;YACE;YACA;SACD;QACD;YACE;YACA;SACD;QACD;YACE;YACA;SACD;QACD;YACE;YACA;SACD;QACD;YACE;YACA;SACD;QACD;YACE;YACA;SACD;QACD;YACE;YACA;SACD;QACD;YACE;YACA;SACD;QACD;YACE;YACA;SACD;QACD;YACE;YACA;SACD;QACD;YACE;YACA;SACD;QACD;YACE;YACA;SACD;QACD;YACE;YACA;SACD;QACD;YACE;YACA;SACD;QACD;YACE;YACA;SACD;QACD;YACE;YACA;SACD;QACD;YACE;YACA;SACD;QACD;YACE;YACA;SACD;QACD;YACE;YACA;SACD;QACD;YACE;YACA;SACD;QACD;YACE;YACA;SACD;QACD;YACE;YACA;SACD;QACD;YACE;YACA;SACD;QACD;YACE;YACA;SACD;QACD;YACE;YACA;SACD;QACD;YAAC;YAA4B;SAA0C;QACvE;YACE;YACA;SACD;QACD;YAAC;YAA4B;SAA0C;QACvE;YAAC;YAAmB;SAAqC;QACzD;YAAC;YAAuB;SAAqC;QAC7D;YAAC;YAA6B;SAAqC;QACnE;YAAC;YAAgC;SAAqC;QACtE;YAAC;YAAkB;SAAiD;QACpE;YACE;YACA;SACD;KACF,CAACC,GAAG,CACH,+FAA+F;IAC/F,CAAC,CAACC,SAASC,YAAY,GAAK;YAACD;YAASH,QAAQK,OAAO,CAACD;SAAa;AAGzE"}