{"version": 3, "sources": ["../../../../src/server/web/sandbox/fetch-inline-assets.ts"], "names": ["fetchInlineAsset", "options", "inputString", "String", "input", "startsWith", "name", "replace", "asset", "assets", "find", "x", "filePath", "resolve", "distDir", "fileIsReadable", "fs", "access", "then", "readStream", "createReadStream", "context", "Response", "requestToBodyStream", "Uint8Array"], "mappings": ";;;;+BAUsBA;;;eAAAA;;;oBAT2B;6BACb;sBACZ;AAOjB,eAAeA,iBAAiBC,OAKtC;IACC,MAAMC,cAAcC,OAAOF,QAAQG,KAAK;IACxC,IAAI,CAACF,YAAYG,UAAU,CAAC,UAAU;QACpC;IACF;IAEA,MAAMC,OAAOJ,YAAYK,OAAO,CAAC,SAAS;IAC1C,MAAMC,QAAQP,QAAQQ,MAAM,GACxBR,QAAQQ,MAAM,CAACC,IAAI,CAAC,CAACC,IAAMA,EAAEL,IAAI,KAAKA,QACtC;QACEA;QACAM,UAAUN;IACZ;IACJ,IAAI,CAACE,OAAO;QACV;IACF;IAEA,MAAMI,WAAWC,IAAAA,aAAO,EAACZ,QAAQa,OAAO,EAAEN,MAAMI,QAAQ;IACxD,MAAMG,iBAAiB,MAAMC,YAAE,CAACC,MAAM,CAACL,UAAUM,IAAI,CACnD,IAAM,MACN,IAAM;IAGR,IAAIH,gBAAgB;QAClB,MAAMI,aAAaC,IAAAA,oBAAgB,EAACR;QACpC,OAAO,IAAIX,QAAQoB,OAAO,CAACC,QAAQ,CACjCC,IAAAA,gCAAmB,EAACtB,QAAQoB,OAAO,EAAEG,YAAYL;IAErD;AACF"}