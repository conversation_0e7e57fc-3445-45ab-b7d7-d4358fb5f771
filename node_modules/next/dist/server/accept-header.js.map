{"version": 3, "sources": ["../../src/server/accept-header.ts"], "names": ["acceptLanguage", "parse", "raw", "preferences", "options", "lowers", "Map", "header", "replace", "pos", "preference", "lower", "toLowerCase", "set", "orig", "prefixMatch", "parts", "split", "pop", "length", "joined", "join", "has", "selections", "map", "Set", "i", "part", "params", "Error", "type", "token", "selection", "q", "pref", "get", "add", "key", "value", "score", "parseFloat", "Number", "isFinite", "push", "sort", "a", "b", "undefined", "values", "preferred"], "mappings": ";;;;+BAiIgBA;;;eAAAA;;;AArHhB,SAASC,MACPC,GAAW,EACXC,WAAiC,EACjCC,OAAgB;IAEhB,MAAMC,SAAS,IAAIC;IACnB,MAAMC,SAASL,IAAIM,OAAO,CAAC,UAAU;IAErC,IAAIL,aAAa;QACf,IAAIM,MAAM;QACV,KAAK,MAAMC,cAAcP,YAAa;YACpC,MAAMQ,QAAQD,WAAWE,WAAW;YACpCP,OAAOQ,GAAG,CAACF,OAAO;gBAAEG,MAAMJ;gBAAYD,KAAKA;YAAM;YACjD,IAAIL,QAAQW,WAAW,EAAE;gBACvB,MAAMC,QAAQL,MAAMM,KAAK,CAAC;gBAC1B,MAAQD,MAAME,GAAG,IAAIF,MAAMG,MAAM,GAAG,EAAI;oBACtC,MAAMC,SAASJ,MAAMK,IAAI,CAAC;oBAC1B,IAAI,CAAChB,OAAOiB,GAAG,CAACF,SAAS;wBACvBf,OAAOQ,GAAG,CAACO,QAAQ;4BAAEN,MAAMJ;4BAAYD,KAAKA;wBAAM;oBACpD;gBACF;YACF;QACF;IACF;IAEA,MAAMO,QAAQT,OAAOU,KAAK,CAAC;IAC3B,MAAMM,aAA0B,EAAE;IAClC,MAAMC,MAAM,IAAIC;IAEhB,IAAK,IAAIC,IAAI,GAAGA,IAAIV,MAAMG,MAAM,EAAE,EAAEO,EAAG;QACrC,MAAMC,OAAOX,KAAK,CAACU,EAAE;QACrB,IAAI,CAACC,MAAM;YACT;QACF;QAEA,MAAMC,SAASD,KAAKV,KAAK,CAAC;QAC1B,IAAIW,OAAOT,MAAM,GAAG,GAAG;YACrB,MAAM,IAAIU,MAAM,CAAC,QAAQ,EAAEzB,QAAQ0B,IAAI,CAAC,OAAO,CAAC;QAClD;QAEA,IAAIC,QAAQH,MAAM,CAAC,EAAE,CAAChB,WAAW;QACjC,IAAI,CAACmB,OAAO;YACV,MAAM,IAAIF,MAAM,CAAC,QAAQ,EAAEzB,QAAQ0B,IAAI,CAAC,OAAO,CAAC;QAClD;QAEA,MAAME,YAAuB;YAAED;YAAOtB,KAAKiB;YAAGO,GAAG;QAAE;QACnD,IAAI9B,eAAeE,OAAOiB,GAAG,CAACS,QAAQ;YACpCC,UAAUE,IAAI,GAAG7B,OAAO8B,GAAG,CAACJ,OAAQtB,GAAG;QACzC;QAEAe,IAAIY,GAAG,CAACJ,UAAUD,KAAK;QAEvB,IAAIH,OAAOT,MAAM,KAAK,GAAG;YACvB,MAAMc,IAAIL,MAAM,CAAC,EAAE;YACnB,MAAM,CAACS,KAAKC,MAAM,GAAGL,EAAEhB,KAAK,CAAC;YAE7B,IAAI,CAACqB,SAAUD,QAAQ,OAAOA,QAAQ,KAAM;gBAC1C,MAAM,IAAIR,MAAM,CAAC,QAAQ,EAAEzB,QAAQ0B,IAAI,CAAC,OAAO,CAAC;YAClD;YAEA,MAAMS,QAAQC,WAAWF;YACzB,IAAIC,UAAU,GAAG;gBACf;YACF;YAEA,IAAIE,OAAOC,QAAQ,CAACH,UAAUA,SAAS,KAAKA,SAAS,OAAO;gBAC1DP,UAAUC,CAAC,GAAGM;YAChB;QACF;QAEAhB,WAAWoB,IAAI,CAACX;IAClB;IAEAT,WAAWqB,IAAI,CAAC,CAACC,GAAGC;QAClB,IAAIA,EAAEb,CAAC,KAAKY,EAAEZ,CAAC,EAAE;YACf,OAAOa,EAAEb,CAAC,GAAGY,EAAEZ,CAAC;QAClB;QAEA,IAAIa,EAAEZ,IAAI,KAAKW,EAAEX,IAAI,EAAE;YACrB,IAAIW,EAAEX,IAAI,KAAKa,WAAW;gBACxB,OAAO;YACT;YAEA,IAAID,EAAEZ,IAAI,KAAKa,WAAW;gBACxB,OAAO,CAAC;YACV;YAEA,OAAOF,EAAEX,IAAI,GAAGY,EAAEZ,IAAI;QACxB;QAEA,OAAOW,EAAEpC,GAAG,GAAGqC,EAAErC,GAAG;IACtB;IAEA,MAAMuC,SAASzB,WAAWC,GAAG,CAAC,CAACQ,YAAcA,UAAUD,KAAK;IAC5D,IAAI,CAAC5B,eAAe,CAACA,YAAYgB,MAAM,EAAE;QACvC,OAAO6B;IACT;IAEA,MAAMC,YAAsB,EAAE;IAC9B,KAAK,MAAMjB,aAAagB,OAAQ;QAC9B,IAAIhB,cAAc,KAAK;YACrB,KAAK,MAAM,CAACtB,YAAY4B,MAAM,IAAIjC,OAAQ;gBACxC,IAAI,CAACmB,IAAIF,GAAG,CAACZ,aAAa;oBACxBuC,UAAUN,IAAI,CAACL,MAAMxB,IAAI;gBAC3B;YACF;QACF,OAAO;YACL,MAAMH,QAAQqB,UAAUpB,WAAW;YACnC,IAAIP,OAAOiB,GAAG,CAACX,QAAQ;gBACrBsC,UAAUN,IAAI,CAACtC,OAAO8B,GAAG,CAACxB,OAAQG,IAAI;YACxC;QACF;IACF;IAEA,OAAOmC;AACT;AAEO,SAASjD,eAAeO,SAAS,EAAE,EAAEJ,WAAsB;IAChE,OACEF,MAAMM,QAAQJ,aAAa;QACzB2B,MAAM;QACNf,aAAa;IACf,EAAE,CAAC,EAAE,IAAI;AAEb"}