{"version": 3, "sources": ["../../../src/telemetry/events/build.ts"], "names": ["EVENT_BUILD_FEATURE_USAGE", "EVENT_NAME_PACKAGE_USED_IN_GET_SERVER_SIDE_PROPS", "eventBuildCompleted", "eventBuildFeatureUsage", "eventBuildOptimize", "eventLintCheckCompleted", "eventPackageUsedInGetServerSideProps", "eventTypeCheckCompleted", "REGEXP_DIRECTORY_DUNDER", "REGEXP_DIRECTORY_TESTS", "REGEXP_FILE_TEST", "EVENT_TYPE_CHECK_COMPLETED", "event", "eventName", "payload", "EVENT_LINT_CHECK_COMPLETED", "EVENT_BUILD_COMPLETED", "pagePaths", "totalPageCount", "length", "hasDunderPages", "some", "path", "test", "hasTestPages", "totalAppPagesCount", "EVENT_BUILD_OPTIMIZED", "staticAppPagesCount", "serverAppPagesCount", "edgeRuntimeAppCount", "edgeRuntimePagesCount", "usages", "map", "featureName", "invocationCount", "packagesUsedInServerSideProps", "packageName", "package"], "mappings": ";;;;;;;;;;;;;;;;;;;;;IA2IaA,yBAAyB;eAAzBA;;IAkDAC,gDAAgD;eAAhDA;;IAhIGC,mBAAmB;eAAnBA;;IAoHAC,sBAAsB;eAAtBA;;IAlEAC,kBAAkB;eAAlBA;;IArEAC,uBAAuB;eAAvBA;;IA0JAC,oCAAoC;eAApCA;;IAnLAC,uBAAuB;eAAvBA;;;AAdhB,MAAMC,0BACJ;AACF,MAAMC,yBAAyB;AAC/B,MAAMC,mBAAmB;AAEzB,MAAMC,6BAA6B;AAS5B,SAASJ,wBAAwBK,KAA8B;IAIpE,OAAO;QACLC,WAAWF;QACXG,SAASF;IACX;AACF;AAEA,MAAMG,6BAA6B;AAe5B,SAASV,wBAAwBO,KAA8B;IAIpE,OAAO;QACLC,WAAWE;QACXD,SAASF;IACX;AACF;AAEA,MAAMI,wBAAwB;AASvB,SAASd,oBACde,SAAmB,EACnBL,KAGC;IAED,OAAO;QACLC,WAAWG;QACXF,SAAS;YACP,GAAGF,KAAK;YACRM,gBAAgBD,UAAUE,MAAM;YAChCC,gBAAgBH,UAAUI,IAAI,CAAC,CAACC,OAC9Bd,wBAAwBe,IAAI,CAACD;YAE/BE,cAAcP,UAAUI,IAAI,CAC1B,CAACC,OACCb,uBAAuBc,IAAI,CAACD,SAASZ,iBAAiBa,IAAI,CAACD;YAE/DG,oBAAoBb,MAAMa,kBAAkB;QAC9C;IACF;AACF;AAEA,MAAMC,wBAAwB;AA0BvB,SAAStB,mBACda,SAAmB,EACnBL,KAGC;IAED,OAAO;QACLC,WAAWa;QACXZ,SAAS;YACP,GAAGF,KAAK;YACRM,gBAAgBD,UAAUE,MAAM;YAChCC,gBAAgBH,UAAUI,IAAI,CAAC,CAACC,OAC9Bd,wBAAwBe,IAAI,CAACD;YAE/BE,cAAcP,UAAUI,IAAI,CAC1B,CAACC,OACCb,uBAAuBc,IAAI,CAACD,SAASZ,iBAAiBa,IAAI,CAACD;YAE/DG,oBAAoBb,MAAMa,kBAAkB;YAC5CE,qBAAqBf,MAAMe,mBAAmB;YAC9CC,qBAAqBhB,MAAMgB,mBAAmB;YAC9CC,qBAAqBjB,MAAMiB,mBAAmB;YAC9CC,uBAAuBlB,MAAMkB,qBAAqB;QACpD;IACF;AACF;AAEO,MAAM9B,4BAA4B;AAsClC,SAASG,uBACd4B,MAA6C;IAE7C,OAAOA,OAAOC,GAAG,CAAC,CAAC,EAAEC,WAAW,EAAEC,eAAe,EAAE,GAAM,CAAA;YACvDrB,WAAWb;YACXc,SAAS;gBACPmB;gBACAC;YACF;QACF,CAAA;AACF;AAEO,MAAMjC,mDACX;AAMK,SAASK,qCACd6B,6BAEC;IAED,OAAOA,8BAA8BH,GAAG,CAAC,CAACI,cAAiB,CAAA;YACzDvB,WAAWZ;YACXa,SAAS;gBACPuB,SAASD;YACX;QACF,CAAA;AACF"}