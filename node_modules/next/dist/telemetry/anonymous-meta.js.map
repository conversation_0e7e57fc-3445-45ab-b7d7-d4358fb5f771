{"version": 3, "sources": ["../../src/telemetry/anonymous-meta.ts"], "names": ["getAnonymousMeta", "traits", "cpus", "os", "NOW_REGION", "process", "env", "systemPlatform", "platform", "systemRelease", "release", "systemArchitecture", "arch", "cpuCount", "length", "cpuModel", "model", "cpuSpeed", "speed", "memoryInMb", "Math", "trunc", "totalmem", "pow", "is<PERSON>ock<PERSON>", "isDockerFunction", "isNowDev", "isWsl", "isWslBoolean", "isCI", "ciEnvironment", "ciName", "name", "nextVersion", "__NEXT_VERSION"], "mappings": ";;;;+BAwBgBA;;;eAAAA;;;iEAxBa;8DACJ;2DACV;gEAEgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkB/B,IAAIC;AAEG,SAASD;IACd,IAAIC,QAAQ;QACV,OAAOA;IACT;IAEA,MAAMC,OAAOC,WAAE,CAACD,IAAI,MAAM,EAAE;IAC5B,MAAM,EAAEE,UAAU,EAAE,GAAGC,QAAQC,GAAG;IAClCL,SAAS;QACP,uBAAuB;QACvBM,gBAAgBJ,WAAE,CAACK,QAAQ;QAC3BC,eAAeN,WAAE,CAACO,OAAO;QACzBC,oBAAoBR,WAAE,CAACS,IAAI;QAC3B,sBAAsB;QACtBC,UAAUX,KAAKY,MAAM;QACrBC,UAAUb,KAAKY,MAAM,GAAGZ,IAAI,CAAC,EAAE,CAACc,KAAK,GAAG;QACxCC,UAAUf,KAAKY,MAAM,GAAGZ,IAAI,CAAC,EAAE,CAACgB,KAAK,GAAG;QACxCC,YAAYC,KAAKC,KAAK,CAAClB,WAAE,CAACmB,QAAQ,KAAKF,KAAKG,GAAG,CAAC,MAAM;QACtD,0BAA0B;QAC1BC,UAAUC,IAAAA,iBAAgB;QAC1BC,UAAUtB,eAAe;QACzBuB,OAAOC,cAAY;QACnBC,MAAMC,QAAcD,IAAI;QACxBE,QAAQ,AAACD,QAAcD,IAAI,IAAIC,QAAcE,IAAI,IAAK;QACtDC,aAAa5B,QAAQC,GAAG,CAAC4B,cAAc;IACzC;IAEA,OAAOjC;AACT"}