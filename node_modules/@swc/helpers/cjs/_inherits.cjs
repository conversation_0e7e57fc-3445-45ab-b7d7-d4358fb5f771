"use strict";

var _set_prototype_of = require("./_set_prototype_of.cjs");

exports._ = exports._inherits = _inherits;
function _inherits(subClass, superClass) {
    if (typeof superClass !== "function" && superClass !== null) {
        throw new TypeError("Super expression must either be null or a function");
    }

    subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, writable: true, configurable: true } });

    if (superClass) _set_prototype_of._(subClass, superClass);
}
