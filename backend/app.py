from fastapi import FastAP<PERSON>
from fastapi.responses import StreamingResponse
from fastapi.middleware.cors import CORSMiddleware
import json
from graph import graph  # import the LangGraph graph we define separately

app = FastAPI()

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


@app.get("/agent")
async def run_agent(query: str):
    async def event_generator():
        # Run LangGraph and yield events as Server-Sent Events (SSE)
        async for chunk in graph.astream({"input": query}, stream_mode="updates"):
            yield f"data: {json.dumps(chunk)}\n\n"

    return StreamingResponse(event_generator(), media_type="text/event-stream")
