from langgraph.graph import StateGraph, START, END
import time

# --- Node definitions ---
def plan_node(state):
    time.sleep(1)
    return {**state, "plan": "call_tool"}

def tool_node(state):
    time.sleep(1)
    return {**state, "tool_result": "Sunny, 70F"}

def summarize_node(state):
    time.sleep(1)
    answer = f"The result is: {state['tool_result']}"
    return {**state, "final_answer": answer}

# --- Graph wiring ---
builder = StateGraph(dict)
builder.add_node("plan", plan_node)
builder.add_node("tool", tool_node)
builder.add_node("summarize", summarize_node)

builder.add_edge(START, "plan")
builder.add_edge("plan", "tool")
builder.add_edge("tool", "summarize")
builder.add_edge("summarize", END)

graph = builder.compile()
