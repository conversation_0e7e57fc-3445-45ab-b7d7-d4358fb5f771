"use client";
import { useState } from "react";

export default function ChatSSE() {
  const [events, setEvents] = useState<string[]>([]);
  const [answer, setAnswer] = useState<string>("");
  const [query, setQuery] = useState<string>("what is the weather in sf");

  const runAgent = () => {
    setEvents([]);
    setAnswer("");

    const es = new EventSource(`http://localhost:8000/agent?query=${encodeURIComponent(query)}`);

    es.onmessage = (event) => {
      const chunk = JSON.parse(event.data);

      // Track all events
      setEvents((prev) => [...prev, JSON.stringify(chunk)]);

      // Get node name from chunk
      const nodeName = Object.keys(chunk)[0];

      // Add node name with "..."
      setAnswer((prev) => prev + nodeName + "... ");

      // If final answer exists, append it
      if (chunk.summarize?.final_answer) {
        setAnswer((prev) => prev + chunk.summarize.final_answer);
      }
    };

    es.onerror = () => {
      es.close();
    };
  };

  return (
    <div className="p-6 space-y-4">
      <div className="flex gap-2">
        <input
          type="text"
          value={query}
          onChange={(e) => setQuery(e.target.value)}
          className="flex-1 border border-gray-300 rounded px-3 py-2"
          placeholder="Enter your query..."
        />
        <button
          onClick={runAgent}
          className="bg-blue-500 text-white px-6 py-2 rounded hover:bg-blue-600"
        >
          Run Agent
        </button>
      </div>

      <h2 className="font-bold text-lg">Streaming Answer</h2>
      <div className="bg-gray-100 p-3 rounded">{answer}</div>

      <h2 className="font-bold text-lg">Debug Events</h2>
      <pre className="text-xs bg-gray-50 p-2 rounded h-40 overflow-y-scroll">
        {events.join("\n")}
      </pre>
    </div>
  );
}
