"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./components/ChatSSE.tsx":
/*!********************************!*\
  !*** ./components/ChatSSE.tsx ***!
  \********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ChatSSE; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\nfunction ChatSSE() {\n    _s();\n    const [events, setEvents] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [answer, setAnswer] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [query, setQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"what is the weather in sf\");\n    const runAgent = ()=>{\n        setEvents([]);\n        setAnswer(\"\");\n        const es = new EventSource(\"http://localhost:8000/agent?query=\".concat(encodeURIComponent(query)));\n        es.onmessage = (event)=>{\n            var _chunk_summarize;\n            const chunk = JSON.parse(event.data);\n            // Track all events\n            setEvents((prev)=>[\n                    ...prev,\n                    JSON.stringify(chunk)\n                ]);\n            // Get node name from chunk\n            const nodeName = Object.keys(chunk)[0];\n            // Add node name with \"...\"\n            setAnswer((prev)=>prev + nodeName + \"... \");\n            // If final answer exists, append it\n            if ((_chunk_summarize = chunk.summarize) === null || _chunk_summarize === void 0 ? void 0 : _chunk_summarize.final_answer) {\n                setAnswer((prev)=>prev + chunk.summarize.final_answer);\n            }\n        };\n        es.onerror = ()=>{\n            es.close();\n        };\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"p-6 space-y-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex gap-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                        type: \"text\",\n                        value: query,\n                        onChange: (e)=>setQuery(e.target.value),\n                        className: \"flex-1 border border-gray-300 rounded px-3 py-2\",\n                        placeholder: \"Enter your query...\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/async-agents-test/components/ChatSSE.tsx\",\n                        lineNumber: 41,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: runAgent,\n                        className: \"bg-blue-500 text-white px-6 py-2 rounded hover:bg-blue-600\",\n                        children: \"Run Agent\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/async-agents-test/components/ChatSSE.tsx\",\n                        lineNumber: 48,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Downloads/async-agents-test/components/ChatSSE.tsx\",\n                lineNumber: 40,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                className: \"font-bold text-lg\",\n                children: \"Streaming Answer\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/async-agents-test/components/ChatSSE.tsx\",\n                lineNumber: 56,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-100 p-3 rounded\",\n                children: answer.split(\"\\n\").map((line, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: line\n                    }, i, false, {\n                        fileName: \"/Users/<USER>/Downloads/async-agents-test/components/ChatSSE.tsx\",\n                        lineNumber: 59,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/async-agents-test/components/ChatSSE.tsx\",\n                lineNumber: 57,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                className: \"font-bold text-lg\",\n                children: \"Debug Events\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/async-agents-test/components/ChatSSE.tsx\",\n                lineNumber: 63,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                className: \"text-xs bg-gray-50 p-2 rounded h-40 overflow-y-scroll\",\n                children: events.join(\"\\n\")\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/async-agents-test/components/ChatSSE.tsx\",\n                lineNumber: 64,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Downloads/async-agents-test/components/ChatSSE.tsx\",\n        lineNumber: 39,\n        columnNumber: 5\n    }, this);\n}\n_s(ChatSSE, \"SzTxD5K4jQ77r0wzK6TTnz7BIZw=\");\n_c = ChatSSE;\nvar _c;\n$RefreshReg$(_c, \"ChatSSE\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/ChatSSE.tsx\n"));

/***/ })

});